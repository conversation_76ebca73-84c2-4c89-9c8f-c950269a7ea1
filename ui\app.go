package ui

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

func MakeUI() fyne.CanvasObject {
	// Zone de texte pour l'entrée JSON
	input := widget.NewMultiLineEntry()
	input.SetPlaceHolder("Entrez votre JSON mal formaté ici...")
	input.Wrapping = fyne.TextWrapWord

	// Zone de texte pour le résultat formaté
	output := widget.NewMultiLineEntry()
	output.Wrapping = fyne.TextWrapWord
	output.Disable()

	// Options d'indentation
	indentOptions := []string{"2 espaces", "4 espaces", "Tabulations"}
	indentSelect := widget.NewSelect(indentOptions, func(s string) {
		// La logique de changement d'indentation sera implémentée
	})
	indentSelect.SetSelected("2 espaces")

	// Bouton de formatage
	formatBtn := widget.NewButton("Formater", func() {
		inputText := input.Text
		if inputText == "" {
			output.SetText("")
			return
		}

		formatter := NewFormatter(indentSelect.Selected)
		formatted, err := formatter.FormatJSON(inputText)
		if err != nil {
			output.SetText(PrettyValidationError(err))
		} else {
			output.SetText(formatted)
		}
	})

	// Rafraîchir le formatage si l'indentation change
	indentSelect.OnChanged = func(s string) {
		if input.Text != "" && output.Text != "" {
			formatBtn.OnTapped()
		}
	}

	// Layout principal
	return container.NewVBox(
		widget.NewLabel("Entrée JSON:"),
		input,
		container.NewHBox(
			formatBtn,
			widget.NewLabel("Indentation:"),
			indentSelect,
		),
		widget.NewLabel("Résultat formaté:"),
		output,
	)
}
