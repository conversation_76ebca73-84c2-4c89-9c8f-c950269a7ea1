package main

import (
    "jsonformatter/ui"

    "fyne.io/fyne/v2/app"
    "fyne.io/fyne/v2/widget"
)

func main() {
    myApp := app.New()
    myApp.SetMetadata(&fyne.AppMetadata{
        ID:   "com.jsonformatter.app",
        Name: "JSON Formatter",
    })

    myWindow := myApp.NewWindow("JSON Formatter")
    myWindow.Resize(fyne.NewSize(800, 600))

    content := ui.MakeUI()
    myWindow.SetContent(content)

    myWindow.ShowAndRun()
}